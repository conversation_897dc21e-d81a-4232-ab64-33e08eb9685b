<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'PROMIS Admin Portal' ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/system_images/favicon.ico') ?>">
    <style>
        :root {
            /* Professional Light Theme Color Palette */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-tertiary: #F1F5F9;
            --bg-accent: #EFF6FF;
            
            /* Surface Colors */
            --surface-card: #FFFFFF;
            --surface-card-hover: #F8FAFC;
            --surface-overlay: rgba(255, 255, 255, 0.95);
            --surface-sidebar: #1E293B;
            --surface-header: #FFFFFF;
            
            /* Professional Gradients */
            --gradient-primary: linear-gradient(135deg, #3B82F6, #1D4ED8);
            --gradient-secondary: linear-gradient(135deg, #10B981, #059669);
            --gradient-accent: linear-gradient(135deg, #8B5CF6, #7C3AED);
            
            /* Text Colors */
            --text-primary: #1E293B;
            --text-secondary: #475569;
            --text-tertiary: #64748B;
            --text-muted: #94A3B8;
            --text-white: #FFFFFF;
            --text-sidebar: #E2E8F0;
            --text-sidebar-muted: #94A3B8;
            
            /* Brand Colors */
            --brand-primary: #3B82F6;
            --brand-secondary: #10B981;
            --brand-accent: #8B5CF6;
            --brand-danger: #EF4444;
            --brand-warning: #F59E0B;
            
            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            
            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            
            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            
            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            
            /* Layout */
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
            --header-height: 70px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Layout Structure */
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar - Standard Feature */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--surface-sidebar);
            position: fixed;
            height: 100vh;
            left: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: var(--spacing-xl);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: var(--spacing-lg) var(--spacing-md);
        }

        .sidebar-logo {
            font-size: 1.5rem;
            font-weight: 700;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .sidebar-logo {
            font-size: 1rem;
        }

        .sidebar-nav {
            padding: var(--spacing-lg);
        }

        .nav-item {
            margin-bottom: var(--spacing-sm);
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: var(--spacing-md);
            color: var(--text-sidebar-muted);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
            position: relative;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-sidebar);
        }

        .nav-link.active {
            background: var(--gradient-primary);
            color: var(--text-white);
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            margin-right: var(--spacing-md);
            flex-shrink: 0;
            font-size: 1.125rem;
        }

        .sidebar.collapsed .nav-text {
            display: none;
        }

        .sidebar.collapsed .nav-icon {
            margin-right: 0;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease;
        }

        .sidebar.collapsed + .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* Header - Standard Feature */
        .header {
            height: var(--header-height);
            background: var(--surface-header);
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-xl);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: var(--shadow-sm);
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .sidebar-toggle {
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.25rem;
            cursor: pointer;
            padding: var(--spacing-sm);
            border-radius: var(--radius-sm);
            transition: all 0.3s ease;
            margin-right: var(--spacing-lg);
        }

        .sidebar-toggle:hover {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .user-menu {
            position: relative;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--gradient-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-white);
        }

        .user-avatar:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        /* Content Area */
        .content {
            padding: var(--spacing-xl);
            min-height: calc(100vh - var(--header-height));
        }

        /* Flash Messages - Standard Feature */
        .flash-message {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            border-left: 4px solid;
        }

        .flash-success {
            background: #F0FDF4;
            border-color: var(--brand-secondary);
            color: #166534;
        }

        .flash-error {
            background: #FEF2F2;
            border-color: var(--brand-danger);
            color: #991B1B;
        }

        .flash-warning {
            background: #FFFBEB;
            border-color: var(--brand-warning);
            color: #92400E;
        }

        /* Cards - Standard Feature */
        .card {
            background: var(--surface-card);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-md);
            transition: all 0.3s ease;
            margin-bottom: var(--spacing-xl);
            border: 1px solid #E5E7EB;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid #E5E7EB;
        }

        /* Buttons - Standard Feature */
        .btn {
            display: inline-block;
            padding: var(--spacing-md) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-white);
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--bg-tertiary);
            color: var(--text-secondary);
            border: 1px solid #D1D5DB;
        }

        .btn-secondary:hover {
            background: #E5E7EB;
            color: var(--text-primary);
        }

        .btn-danger {
            background: var(--brand-danger);
            color: var(--text-white);
        }

        .btn-danger:hover {
            background: #DC2626;
            transform: translateY(-1px);
        }

        /* Form Elements - Standard Feature */
        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-sm);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            background: var(--surface-card);
            border: 1px solid #D1D5DB;
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 0.875rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .form-input:disabled {
            background: var(--bg-tertiary);
            color: var(--text-muted);
            cursor: not-allowed;
        }

        /* Tables - Standard Feature */
        .table-container {
            background: var(--surface-card);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            border: 1px solid #E5E7EB;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: var(--spacing-md);
            text-align: left;
            border-bottom: 1px solid #E5E7EB;
        }

        .table th {
            background: var(--bg-tertiary);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
        }

        .table td {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .table tr:hover {
            background: var(--bg-accent);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content {
                padding: var(--spacing-md);
            }

            .header {
                padding: 0 var(--spacing-md);
            }
        }

        /* Utility Classes */
        .text-center { text-align: center; }
        .text-left { text-align: left; }
        .text-right { text-align: right; }
        .mb-lg { margin-bottom: var(--spacing-lg); }
        .mb-xl { margin-bottom: var(--spacing-xl); }
        .mt-lg { margin-top: var(--spacing-lg); }
        .mt-xl { margin-top: var(--spacing-xl); }
        .d-flex { display: flex; }
        .align-items-center { align-items: center; }
        .justify-content-between { justify-content: space-between; }
        .gap-md { gap: var(--spacing-md); }
    </style>
    <?= isset($additional_css) ? $additional_css : '' ?>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar - Standard across all admin pages -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">PROMIS</div>
            </div>
            <nav class="sidebar-nav">
                <!-- Standard Navigation Items -->
                <div class="nav-item">
                    <a href="<?= base_url('admin/dashboard') ?>" class="nav-link <?= (current_url() == base_url('admin/dashboard')) ? 'active' : '' ?>">
                        <span class="nav-icon">📊</span>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('admin/projects') ?>" class="nav-link <?= (strpos(current_url(), 'admin/projects') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">📁</span>
                        <span class="nav-text">Projects</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('admin/users') ?>" class="nav-link <?= (strpos(current_url(), 'admin/users') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">👥</span>
                        <span class="nav-text">Users</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('admin/organizations') ?>" class="nav-link <?= (strpos(current_url(), 'admin/organizations') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">🏢</span>
                        <span class="nav-text">Organizations</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('admin/reports') ?>" class="nav-link <?= (strpos(current_url(), 'admin/reports') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">📈</span>
                        <span class="nav-text">Reports</span>
                    </a>
                </div>
                <div class="nav-item">
                    <a href="<?= base_url('admin/settings') ?>" class="nav-link <?= (strpos(current_url(), 'admin/settings') !== false) ? 'active' : '' ?>">
                        <span class="nav-icon">⚙️</span>
                        <span class="nav-text">Settings</span>
                    </a>
                </div>

                <!-- Logout - Always at bottom -->
                <div class="nav-item" style="margin-top: auto; border-top: 1px solid rgba(255, 255, 255, 0.1); padding-top: var(--spacing-md);">
                    <a href="<?= base_url('auth/logout') ?>" class="nav-link">
                        <span class="nav-icon">🚪</span>
                        <span class="nav-text">Logout</span>
                    </a>
                </div>

                <!-- User Info - Standard across all pages -->
                <div class="nav-item" style="margin-top: var(--spacing-md); padding: var(--spacing-md); border-top: 1px solid rgba(255, 255, 255, 0.1);">
                    <div class="user-info" style="display: flex; align-items: center; gap: var(--spacing-sm);">
                        <div class="user-avatar-small" style="width: 32px; height: 32px; border-radius: 50%; background: var(--gradient-secondary); display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem; color: var(--text-white);">
                            <?= strtoupper(substr(session()->get('admin_username') ?? 'A', 0, 1)) ?>
                        </div>
                        <div class="user-details" style="flex: 1;">
                            <div class="username" style="color: var(--text-sidebar); font-weight: 600; font-size: 0.875rem; margin-bottom: var(--spacing-xs);">
                                <?= esc(session()->get('admin_username') ?? 'Admin User') ?>
                            </div>
                            <div class="user-role" style="color: var(--text-sidebar-muted); font-size: 0.75rem; text-transform: uppercase; letter-spacing: 0.05em;">
                                <?= esc(session()->get('admin_role') ?? 'Administrator') ?>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header - Standard across all admin pages -->
            <header class="header">
                <div class="header-left">
                    <button class="sidebar-toggle" id="sidebarToggle">☰</button>
                    <div>
                        <h1 class="page-title"><?= isset($page_title) ? $page_title : 'Dashboard' ?></h1>
                        <?php if (session()->get('admin_organization_name')): ?>
                            <div style="font-size: 0.75rem; color: var(--text-muted); margin-top: -0.25rem;">
                                <?= esc(session()->get('admin_organization_name')) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="header-right">
                    <!-- Header actions section - for page-specific buttons/actions -->
                    <?= $this->renderSection('header_actions') ?>
                    <div class="user-menu">
                        <div class="user-avatar" title="<?= session()->get('admin_username') ?? 'Admin User' ?>">
                            <?= strtoupper(substr(session()->get('admin_username') ?? 'A', 0, 1)) ?>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <div class="content">
                <!-- Flash Messages - Standard across all pages -->
                <?php if (session()->getFlashdata('success')): ?>
                    <div class="flash-message flash-success">
                        <?= session()->getFlashdata('success') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('error')): ?>
                    <div class="flash-message flash-error">
                        <?= session()->getFlashdata('error') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('warning')): ?>
                    <div class="flash-message flash-warning">
                        <?= session()->getFlashdata('warning') ?>
                    </div>
                <?php endif; ?>

                <?php if (session()->getFlashdata('errors')): ?>
                    <div class="flash-message flash-error">
                        <ul style="margin: 0; padding-left: var(--spacing-md);">
                            <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Page Content - This is where individual pages render their content -->
                <?= $this->renderSection('content') ?>
            </div>
        </main>
    </div>

    <!-- Standard JavaScript - Across all admin pages -->
    <script>
        // Sidebar Toggle Functionality
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');

            // Store preference in localStorage
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        });

        // Restore sidebar state on page load
        document.addEventListener('DOMContentLoaded', function() {
            const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (isCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
            }
        });

        // Mobile sidebar toggle
        function toggleMobileSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('mobile-open');
        }

        // Close mobile sidebar when clicking outside
        document.addEventListener('click', function(e) {
            const sidebar = document.getElementById('sidebar');
            const toggle = document.getElementById('sidebarToggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(e.target) &&
                !toggle.contains(e.target) &&
                sidebar.classList.contains('mobile-open')) {
                sidebar.classList.remove('mobile-open');
            }
        });

        // Auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });
        });
    </script>

    <!-- Additional JavaScript section for page-specific scripts -->
    <?= isset($additional_js) ? $additional_js : '' ?>
</body>
</html>
