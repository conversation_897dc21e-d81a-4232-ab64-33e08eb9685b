<?php

namespace App\Controllers\Admin;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;
use App\Models\ProjectModel;

/**
 * Admin Dashboard Controller
 * 
 * Handles the main dashboard for the PROMIS Admin Portal including:
 * - Dashboard display with statistics
 * - Widget layout customization
 * - Theme switching
 */
class AdminDashboardController extends BaseController
{
    protected $userModel;
    protected $organizationModel;
    protected $projectModel;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->organizationModel = new OrganizationModel();
        $this->projectModel = new ProjectModel();
    }

    /**
     * Display main dashboard - GET request
     */
    public function index()
    {
        // Get dashboard statistics
        $stats = $this->getDashboardStats();
        
        // Get recent activities (placeholder for now)
        $recentActivities = $this->getRecentActivities();
        
        $data = [
            'title' => 'Dashboard - PROMIS Admin',
            'page_title' => 'Dashboard',
            'stats' => $stats,
            'recent_activities' => $recentActivities
        ];

        return view('admin/admin_dashboard', $data);
    }

    /**
     * Save widget layout - POST request
     */
    public function saveWidgetLayout()
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid security token'
            ]);
        }

        $layout = $this->request->getPost('layout');
        $userId = session()->get('admin_user_id');

        if (!$layout || !$userId) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid layout data'
            ]);
        }

        // Store layout in session for now (could be stored in database)
        session()->set('admin_dashboard_layout', $layout);

        // Log the action
        $this->logAuditEvent('update', $userId, 'dashboard_layout', $userId, [
            'description' => 'Dashboard layout updated',
            'layout_data' => $layout
        ]);

        return $this->response->setJSON([
            'success' => true,
            'message' => 'Dashboard layout saved successfully'
        ]);
    }

    /**
     * Switch theme - POST request
     */
    public function switchTheme()
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token');
        }

        $theme = $this->request->getPost('theme');
        $userId = session()->get('admin_user_id');

        if (!in_array($theme, ['light', 'dark', 'auto'])) {
            return redirect()->back()->with('error', 'Invalid theme selection');
        }

        // Store theme preference in session
        session()->set('admin_theme', $theme);

        // In a real implementation, you might store this in user preferences table
        // For now, we'll use session storage

        // Log the action
        $this->logAuditEvent('update', $userId, 'user_preferences', $userId, [
            'description' => 'Theme switched to ' . $theme,
            'theme' => $theme
        ]);

        return redirect()->back()->with('success', 'Theme switched to ' . ucfirst($theme) . ' mode');
    }

    /**
     * Get dashboard widget layout - GET request
     */
    public function getWidgetLayout()
    {
        $userId = session()->get('admin_user_id');
        $layout = session()->get('admin_dashboard_layout');

        // Default layout if none exists
        if (!$layout) {
            $layout = [
                'widgets' => [
                    ['id' => 'stats', 'position' => 1, 'enabled' => true],
                    ['id' => 'recent_activities', 'position' => 2, 'enabled' => true],
                    ['id' => 'quick_actions', 'position' => 3, 'enabled' => true],
                    ['id' => 'system_info', 'position' => 4, 'enabled' => true]
                ]
            ];
        }

        return $this->response->setJSON([
            'success' => true,
            'layout' => $layout
        ]);
    }

    /**
     * Reset dashboard to default layout - POST request
     */
    public function resetDashboard()
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->with('error', 'Invalid security token');
        }

        $userId = session()->get('admin_user_id');

        // Clear saved layout
        session()->remove('admin_dashboard_layout');

        // Log the action
        $this->logAuditEvent('update', $userId, 'dashboard_layout', $userId, [
            'description' => 'Dashboard layout reset to default',
            'action_type' => 'reset_layout'
        ]);

        return redirect()->back()->with('success', 'Dashboard layout reset to default');
    }

    /**
     * Get dashboard statistics
     */
    private function getDashboardStats()
    {
        $stats = [
            'total_users' => $this->userModel->where('deleted_at', null)->countAllResults(),
            'active_users' => $this->userModel->where('deleted_at', null)
                                             ->where('is_activated', 1)
                                             ->countAllResults(),
            'total_organizations' => $this->organizationModel->where('deleted_at', null)->countAllResults(),
            'active_organizations' => $this->organizationModel->where('deleted_at', null)
                                                              ->where('is_active', 1)
                                                              ->countAllResults(),
            'total_projects' => $this->projectModel->where('deleted_at', null)->countAllResults(),
            'active_projects' => $this->projectModel->where('deleted_at', null)
                                                   ->where('status', 'active')
                                                   ->countAllResults()
        ];

        return $stats;
    }

    /**
     * Get recent activities (placeholder)
     */
    private function getRecentActivities()
    {
        // This would typically query the audit_logs table
        // For now, return sample data
        return [
            [
                'action' => 'User Created',
                'description' => 'New user account created for John Doe',
                'time' => '2 hours ago',
                'type' => 'user'
            ],
            [
                'action' => 'Project Updated',
                'description' => 'Project "Road Construction" status changed to active',
                'time' => '4 hours ago',
                'type' => 'project'
            ],
            [
                'action' => 'Organization Registered',
                'description' => 'New organization "ABC Construction" registered',
                'time' => '1 day ago',
                'type' => 'organization'
            ]
        ];
    }

    /**
     * Log audit event
     */
    private function logAuditEvent($action, $userId, $tableName, $primaryKey, $data = [])
    {
        $auditData = [
            'table_name' => $tableName,
            'primary_key' => (string)$primaryKey,
            'action' => $action,
            'new_data' => json_encode($data),
            'user_id' => $userId,
            'username' => session()->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => session()->get('admin_user_name'),
            'organization_id' => session()->get('admin_organization_id'),
            'organization_name' => session()->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'portal' => 'admin',
            'module' => 'dashboard',
            'ip_address' => $this->request->getIPAddress(),
            'user_agent' => $this->request->getUserAgent(),
            'session_id' => session()->session_id,
            'request_url' => current_url(),
            'description' => $data['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $db = \Config\Database::connect();
        $db->table('audit_logs')->insert($auditData);
    }
}
