<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

/**
 * Admin Authentication Filter
 * 
 * Handles session validation and access control for the PROMIS Admin Portal
 * - Checks for valid admin session
 * - Manages session timeout (30 minutes)
 * - Updates last activity timestamp
 * - <PERSON><PERSON> remember me auto-login
 */
class AdminAuthFilter implements FilterInterface
{
    /**
     * Check authentication before request
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $session = session();
        
        // Check if user is already logged in
        if ($session->get('admin_logged_in') && $session->get('admin_user_id')) {
            
            // Check session timeout (30 minutes = 1800 seconds)
            $lastActivity = $session->get('admin_last_activity');
            $sessionTimeout = 1800; // 30 minutes
            
            if ($lastActivity && (time() - $lastActivity) > $sessionTimeout) {
                // Session expired
                $this->logSessionExpiry($session);
                $session->destroy();
                return redirect()->to(base_url('admin/login'))
                                ->with('warning', 'Your session has expired. Please login again.');
            }
            
            // Update last activity
            $session->set('admin_last_activity', time());
            
            return; // User is authenticated, continue
        }
        
        // Check for remember me token
        if (isset($_COOKIE['admin_remember_token'])) {
            if ($this->handleRememberMeLogin()) {
                return; // Auto-login successful, continue
            }
        }
        
        // User not authenticated, redirect to login
        return redirect()->to(base_url('admin/login'))
                        ->with('warning', 'Please login to access the admin portal.');
    }

    /**
     * After request processing
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // Nothing to do after request
    }

    /**
     * Handle remember me auto-login
     */
    private function handleRememberMeLogin()
    {
        $tokenData = $_COOKIE['admin_remember_token'];
        
        if (!$tokenData || !str_contains($tokenData, '|')) {
            // Invalid token format
            $this->clearRememberMeToken();
            return false;
        }
        
        list($token, $userId) = explode('|', $tokenData, 2);
        
        if (!$token || !$userId || !is_numeric($userId)) {
            // Invalid token data
            $this->clearRememberMeToken();
            return false;
        }
        
        // Load user model and get user
        $userModel = new \App\Models\UserModel();
        $user = $userModel->find($userId);
        
        if (!$user || !$user['is_activated'] || $user['deleted_at']) {
            // User not found, not activated, or deleted
            $this->clearRememberMeToken();
            return false;
        }
        
        // Get organization details
        $organizationModel = new \App\Models\OrganizationModel();
        $organization = $organizationModel->find($user['organization_id']);
        
        // Set session variables for auto-login
        $sessionData = [
            'admin_user_id' => $user['id'],
            'admin_username' => $user['username'],
            'admin_user_name' => $user['name'],
            'admin_user_role' => $user['role'],
            'admin_organization_id' => $user['organization_id'],
            'admin_organization_name' => $organization ? $organization['name'] : 'Unknown',
            'admin_logged_in' => true,
            'admin_last_activity' => time(),
            'admin_auto_login' => true
        ];
        
        session()->set($sessionData);
        
        // Update last login
        $userModel->update($user['id'], ['last_login_at' => date('Y-m-d H:i:s')]);
        
        // Log auto-login event
        $this->logAuditEvent('login', $user['id'], 'users', $user['id'], [
            'description' => 'Admin user ' . $user['username'] . ' auto-logged in via remember me token',
            'event_type' => 'authentication',
            'auto_login' => true
        ]);
        
        // Regenerate remember me token for security
        $this->regenerateRememberMeToken($userId);
        
        return true;
    }

    /**
     * Clear remember me token
     */
    private function clearRememberMeToken()
    {
        if (isset($_COOKIE['admin_remember_token'])) {
            setcookie('admin_remember_token', '', time() - 3600, '/');
        }
    }

    /**
     * Regenerate remember me token
     */
    private function regenerateRememberMeToken($userId)
    {
        $newToken = bin2hex(random_bytes(32));
        
        $cookieOptions = [
            'expires' => time() + (30 * 24 * 60 * 60), // 30 days
            'path' => '/',
            'secure' => false, // Set to true in production with HTTPS
            'httponly' => true,
            'samesite' => 'Lax'
        ];
        
        setcookie('admin_remember_token', $newToken . '|' . $userId, $cookieOptions);
    }

    /**
     * Log session expiry
     */
    private function logSessionExpiry($session)
    {
        $userId = $session->get('admin_user_id');
        $username = $session->get('admin_username');
        
        if ($userId) {
            $this->logAuditEvent('logout', $userId, 'users', $userId, [
                'description' => 'Admin user ' . $username . ' session expired',
                'event_type' => 'authentication',
                'session_expired' => true
            ]);
        }
    }

    /**
     * Log audit event
     */
    private function logAuditEvent($action, $userId, $tableName, $primaryKey, $data = [])
    {
        $request = service('request');
        $session = session();
        
        $auditData = [
            'table_name' => $tableName,
            'primary_key' => (string)$primaryKey,
            'action' => $action,
            'new_data' => json_encode($data),
            'user_id' => $userId,
            'username' => $session->get('admin_username'),
            'user_type' => 'admin_user',
            'user_full_name' => $session->get('admin_user_name'),
            'organization_id' => $session->get('admin_organization_id'),
            'organization_name' => $session->get('admin_organization_name'),
            'organization_type' => 'Organization',
            'portal' => 'admin',
            'module' => 'authentication',
            'ip_address' => $request->getIPAddress(),
            'user_agent' => $request->getUserAgent(),
            'session_id' => $session->session_id,
            'request_url' => current_url(),
            'description' => $data['description'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];

        $db = \Config\Database::connect();
        $db->table('audit_logs')->insert($auditData);
    }
}
