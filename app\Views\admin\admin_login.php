<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title : 'PROMIS Portal Login' ?></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="<?= base_url('public/system_images/favicon.ico') ?>">
    <style>
        :root {
            /* PROMIS Professional Color Palette */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8FAFC;
            --bg-tertiary: #F1F5F9;
            --bg-accent: #EFF6FF;

            /* Surface Colors */
            --surface-card: rgba(255, 255, 255, 0.95);
            --surface-card-hover: rgba(255, 255, 255, 0.98);
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(59, 130, 246, 0.2);

            /* Professional Gradients */
            --gradient-primary: linear-gradient(135deg, #3B82F6, #1D4ED8);
            --gradient-secondary: linear-gradient(135deg, #10B981, #059669);
            --gradient-accent: linear-gradient(135deg, #8B5CF6, #7C3AED);
            --gradient-hero: linear-gradient(135deg, #667EEA 0%, #764BA2 100%);

            /* Text Colors */
            --text-primary: #1E293B;
            --text-secondary: #475569;
            --text-tertiary: #64748B;
            --text-muted: #94A3B8;
            --text-white: #FFFFFF;

            /* Brand Colors */
            --brand-primary: #3B82F6;
            --brand-secondary: #10B981;
            --brand-accent: #8B5CF6;

            /* Typography */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

            /* Spacing */
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --spacing-2xl: 3rem;
            --spacing-3xl: 4rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-glow-primary: 0 0 20px rgba(59, 130, 246, 0.3);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-primary);
            background: var(--gradient-hero);
            color: var(--text-primary);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        /* Background Pattern */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
            z-index: -1;
        }

        /* Login Container */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: var(--spacing-xl);
        }

        .login-card {
            background: var(--surface-card);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-2xl);
            padding: var(--spacing-3xl);
            width: 100%;
            max-width: 420px;
            box-shadow: var(--shadow-xl);
            transition: all 0.3s ease;
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-xl), var(--shadow-glow-primary);
        }

        .logo {
            text-align: center;
            margin-bottom: var(--spacing-3xl);
        }

        .logo h1 {
            font-size: 2.75rem;
            font-weight: 800;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: var(--spacing-sm);
            letter-spacing: -0.02em;
        }

        .logo p {
            color: var(--text-secondary);
            font-size: 0.95rem;
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
        }

        .logo .subtitle {
            color: var(--text-muted);
            font-size: 0.8rem;
            font-weight: 400;
        }

        .form-group {
            margin-bottom: var(--spacing-xl);
        }

        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            margin-bottom: var(--spacing-sm);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-lg);
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            border-radius: var(--radius-lg);
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--brand-primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-1px);
        }

        .form-input::placeholder {
            color: var(--text-muted);
            font-weight: 400;
        }

        .btn {
            display: inline-block;
            padding: var(--spacing-lg) var(--spacing-xl);
            border: none;
            border-radius: var(--radius-lg);
            font-size: 1rem;
            font-weight: 600;
            text-decoration: none;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: var(--text-white);
            width: 100%;
            margin-top: var(--spacing-md);
            box-shadow: var(--shadow-lg);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl), var(--shadow-glow-primary);
        }

        .btn-primary:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-secondary);
            border: 2px solid var(--glass-border);
            backdrop-filter: blur(10px);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-primary);
            border-color: var(--brand-primary);
            text-decoration: none;
        }

        /* Checkbox Styling */
        input[type="checkbox"] {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid var(--glass-border);
            border-radius: var(--radius-sm);
            background: var(--glass-bg);
            cursor: pointer;
            position: relative;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        input[type="checkbox"]:checked {
            background: var(--gradient-primary);
            border-color: transparent;
        }

        input[type="checkbox"]:checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        input[type="checkbox"]:focus {
            outline: 2px solid rgba(59, 130, 246, 0.3);
            outline-offset: 2px;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
        }

        .checkbox-label {
            margin: 0;
            font-size: 0.875rem;
            color: var(--text-secondary);
            font-weight: 500;
            cursor: pointer;
        }

        /* Alert Messages */
        .alert {
            padding: var(--spacing-lg);
            border-radius: var(--radius-lg);
            margin-bottom: var(--spacing-xl);
            border: 1px solid;
            backdrop-filter: blur(10px);
            animation: slideDown 0.3s ease-out;
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.3);
            color: #DC2626;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #059669;
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.3);
            color: #D97706;
        }

        /* Portal Selection */
        .portal-selection {
            text-align: center;
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-xl);
            border-top: 1px solid var(--glass-border);
        }

        .portal-selection p {
            color: var(--text-muted);
            font-size: 0.875rem;
            margin-bottom: var(--spacing-md);
        }

        /* Footer */
        .login-footer {
            text-align: center;
            margin-top: var(--spacing-2xl);
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .login-footer p {
            margin-bottom: var(--spacing-xs);
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: var(--text-white);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .login-container {
                padding: var(--spacing-md);
            }

            .login-card {
                padding: var(--spacing-2xl);
            }

            .logo h1 {
                font-size: 2.25rem;
            }

            .form-input {
                font-size: 16px; /* Prevent zoom on iOS */
            }
        }

        @media (max-width: 480px) {
            .login-card {
                padding: var(--spacing-xl);
            }

            .logo h1 {
                font-size: 2rem;
            }
        }

        /* Form validation styles */
        .form-input:invalid {
            border-color: rgba(239, 68, 68, 0.5);
        }

        .form-input:valid {
            border-color: rgba(16, 185, 129, 0.5);
        }

        /* Focus trap for accessibility */
        .login-card:focus-within {
            box-shadow: var(--shadow-xl), 0 0 0 2px rgba(59, 130, 246, 0.2);
        }

        /* Disable button state */
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn:disabled:hover {
            box-shadow: var(--shadow-lg) !important;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="logo">
                <h1>PROMIS</h1>
                <p>Organization Portal</p>
                <div class="subtitle">Project Management Information Systems</div>
            </div>

            <!-- Alert Messages -->
            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-error">
                    <?= session()->getFlashdata('error') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success">
                    <?= session()->getFlashdata('success') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('warning')): ?>
                <div class="alert alert-warning">
                    <?= session()->getFlashdata('warning') ?>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('errors')): ?>
                <div class="alert alert-error">
                    <ul style="margin: 0; padding-left: 20px;">
                        <?php foreach (session()->getFlashdata('errors') as $error): ?>
                            <li><?= esc($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- Login Form -->
            <form action="<?= base_url('admin/login') ?>" method="post" id="loginForm">
                <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />

                <div class="form-group">
                    <label for="username" class="form-label">Username or Email</label>
                    <input
                        type="text"
                        id="username"
                        name="username"
                        class="form-input"
                        placeholder="Enter your username or email"
                        value="<?= old('username') ?>"
                        required
                        autocomplete="username"
                        autofocus
                    >
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input
                        type="password"
                        id="password"
                        name="password"
                        class="form-input"
                        placeholder="Enter your password"
                        required
                        autocomplete="current-password"
                    >
                </div>

                <div class="checkbox-group">
                    <input
                        type="checkbox"
                        id="remember_me"
                        name="remember_me"
                        value="1"
                    >
                    <label for="remember_me" class="checkbox-label">
                        Remember me for 30 days
                    </label>
                </div>

                <button type="submit" class="btn btn-primary" id="loginBtn">
                    <span id="loginText">Sign In to Portal</span>
                    <span id="loginLoader" class="loading" style="display: none;"></span>
                </button>
            </form>

            <!-- Portal Selection -->
            <div class="portal-selection">
                <p>Need to access a different portal?</p>
                <div style="display: flex; gap: var(--spacing-sm); justify-content: center;">
                    <a href="<?= base_url('dakoii/login') ?>" class="btn btn-secondary" style="flex: 1; font-size: 0.875rem; padding: var(--spacing-md);">
                        Dakoii Portal
                    </a>
                    <a href="<?= base_url('/') ?>" class="btn btn-secondary" style="flex: 1; font-size: 0.875rem; padding: var(--spacing-md);">
                        Public Site
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="login-footer">
            <p>PROMIS v2.0 - Project Management Information Systems</p>
            <p>© <?= date('Y') ?> All Rights Reserved</p>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loginLoader = document.getElementById('loginLoader');

        loginForm.addEventListener('submit', function(e) {
            // Show loading state
            loginBtn.disabled = true;
            loginText.style.display = 'none';
            loginLoader.style.display = 'inline-block';

            // Re-enable after 10 seconds as fallback
            setTimeout(function() {
                loginBtn.disabled = false;
                loginText.style.display = 'inline';
                loginLoader.style.display = 'none';
            }, 10000);
        });

        // Auto-focus on first empty field
        const username = document.getElementById('username');
        const password = document.getElementById('password');

        if (!username.value) {
            username.focus();
        } else if (!password.value) {
            password.focus();
        }

        // Enter key handling
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !loginBtn.disabled) {
                loginForm.submit();
            }
        });
    });
    </script>
</body>
</html>

<style>
/* Login-specific styles */
.admin-login-form .form-input:focus {
    border-color: var(--brand-primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-login-form .btn-primary {
    background: var(--gradient-primary);
    border: none;
    padding: var(--spacing-md);
    font-weight: 600;
    transition: all 0.3s ease;
}

.admin-login-form .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.admin-login-form .btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid #D1D5DB;
    padding: var(--spacing-md);
    font-weight: 500;
    transition: all 0.3s ease;
}

.admin-login-form .btn-secondary:hover {
    background: #E5E7EB;
    color: var(--text-primary);
    text-decoration: none;
}

/* Override template styles for login page */
.sidebar {
    display: none;
}

.main-content {
    margin-left: 0;
}

.header {
    display: none;
}

.content {
    padding: var(--spacing-md);
}

body {
    background: linear-gradient(135deg, #EFF6FF 0%, #F8FAFC 100%);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .col-md-6 {
        padding: var(--spacing-md);
    }
    
    .card {
        margin: var(--spacing-md) 0;
    }
}
</style>

<?= $this->endSection() ?>
