<?php

namespace App\Controllers;

use App\Controllers\BaseController;
use App\Models\UserModel;
use App\Models\OrganizationModel;

/**
 * Main Authentication Controller for PROMIS
 * 
 * Handles unified login for both Admin Portal and Monitoring Portal
 * Uses UserModel for organization users authentication
 */
class AuthController extends BaseController
{
    protected $userModel;
    protected $organizationModel;
    protected $session;

    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->organizationModel = new OrganizationModel();
        $this->session = \Config\Services::session();
    }

    /**
     * Show unified login form - GET request
     * Serves both admin and monitoring portal access
     */
    public function showLoginForm()
    {
        // Check if user is already logged in
        if ($this->session->get('admin_user_id')) {
            return $this->redirectToDashboard();
        }

        $data = [
            'title' => 'Login - PROMIS',
            'page_title' => 'Login to PROMIS',
            'validation' => \Config\Services::validation(),
            'csrf_token' => csrf_token(),
            'csrf_hash' => csrf_hash()
        ];

        return view('auth/login', $data);
    }

    /**
     * Process user authentication - POST request
     * Handles login for both admin and monitoring portals
     */
    public function authenticateUser()
    {
        // Validate CSRF token
        if (!$this->validate(['csrf_token' => 'required'])) {
            return redirect()->back()->withInput()->with('error', 'Invalid security token.');
        }

        // Validation rules
        $rules = [
            'identifier' => 'required|min_length[3]',
            'password' => 'required|min_length[4]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $identifier = $this->request->getPost('identifier');
        $password = $this->request->getPost('password');
        $rememberMe = $this->request->getPost('remember_me') ? true : false;

        // Find user by username or email
        $user = $this->userModel->where('username', $identifier)
                                ->orWhere('email', $identifier)
                                ->where('deleted_at', null)
                                ->first();

        if (!$user) {
            return redirect()->back()->withInput()->with('error', 'Invalid credentials. Please try again.');
        }

        // Check if user is activated
        if (!$user['is_activated']) {
            return redirect()->back()->withInput()->with('error', 'Your account is not activated. Please contact your administrator.');
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            // Log failed login attempt
            log_message('info', 'Failed login attempt for user: ' . $identifier . ' from IP: ' . $this->request->getIPAddress());
            
            return redirect()->back()->withInput()->with('error', 'Invalid credentials. Please try again.');
        }

        // Get organization details
        $organization = null;
        if ($user['organization_id']) {
            $organization = $this->organizationModel->find($user['organization_id']);
        }

        // Create user session
        $this->createUserSession($user, $organization, $rememberMe);

        // Update last login
        $this->userModel->update($user['id'], [
            'last_login_at' => date('Y-m-d H:i:s'),
            'updated_by' => $user['id']
        ]);

        // Log successful login
        log_message('info', 'Successful login for user: ' . $user['username'] . ' (ID: ' . $user['id'] . ') from IP: ' . $this->request->getIPAddress());

        // Redirect to appropriate dashboard
        return $this->redirectToDashboard($user);
    }

    /**
     * Create user session
     */
    private function createUserSession($user, $organization = null, $rememberMe = false)
    {
        $sessionData = [
            'admin_user_id' => $user['id'],
            'admin_username' => $user['username'],
            'admin_email' => $user['email'],
            'admin_name' => $user['name'],
            'admin_role' => $user['role'],
            'admin_organization_id' => $user['organization_id'],
            'admin_organization_name' => $organization ? $organization['name'] : null,
            'admin_logged_in' => true,
            'admin_login_time' => time()
        ];

        $this->session->set($sessionData);

        // Set remember me cookie if requested
        if ($rememberMe) {
            $cookieValue = base64_encode($user['id'] . ':' . $user['username'] . ':' . hash('sha256', $user['password_hash']));
            setcookie('promis_remember', $cookieValue, time() + (30 * 24 * 60 * 60), '/', '', false, true); // 30 days
        }
    }

    /**
     * Redirect to appropriate dashboard based on user role
     */
    private function redirectToDashboard($user = null)
    {
        if (!$user) {
            // If no user provided, get from session
            $userId = $this->session->get('admin_user_id');
            if ($userId) {
                $user = $this->userModel->find($userId);
            }
        }

        if (!$user) {
            return redirect()->to(base_url('auth/login'));
        }

        // Determine dashboard based on user role or permissions
        // For now, we'll use a simple role-based approach
        $role = $user['role'] ?? 'user';
        
        switch (strtolower($role)) {
            case 'admin':
            case 'moderator':
                return redirect()->to(base_url('admin/dashboard'))->with('success', 'Welcome back, ' . $user['name'] . '!');
            
            case 'editor':
            case 'user':
            default:
                return redirect()->to(base_url('monitoring/dashboard'))->with('success', 'Welcome back, ' . $user['name'] . '!');
        }
    }

    /**
     * Logout user - GET request
     */
    public function logoutUser()
    {
        $username = $this->session->get('admin_username');
        
        // Clear session
        $this->session->destroy();
        
        // Clear remember me cookie
        if (isset($_COOKIE['promis_remember'])) {
            setcookie('promis_remember', '', time() - 3600, '/', '', false, true);
        }

        // Log logout
        if ($username) {
            log_message('info', 'User logged out: ' . $username . ' from IP: ' . $this->request->getIPAddress());
        }

        return redirect()->to(base_url('auth/login'))->with('success', 'You have been logged out successfully.');
    }

    /**
     * Show registration form - GET request (for future use)
     */
    public function showRegistrationForm()
    {
        $data = [
            'title' => 'Register - PROMIS',
            'page_title' => 'Create Account'
        ];

        return view('auth/register', $data);
    }

    /**
     * Process user registration - POST request (for future use)
     */
    public function registerUser()
    {
        // Registration logic will be implemented later
        return redirect()->to(base_url('auth/login'))->with('info', 'Registration is currently disabled. Please contact your administrator.');
    }
}
